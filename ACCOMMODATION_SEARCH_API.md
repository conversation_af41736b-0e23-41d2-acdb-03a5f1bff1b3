# Accommodation Search and Filter API

## Overview
The accommodation search endpoint provides flexible search and filtering capabilities for accommodations with pagination and sorting support.

## Endpoint
```
GET /api/accommodations/search
```

## Authorization
- Requires authentication
- Role: `Users` (same as other accommodation GET endpoints)

## Query Parameters

### Search Parameters
- `searchTerm` (optional): Search term to match against accommodation name and address fields (partial, case-insensitive)
- `location` (optional): Location filter to match against suburb, state, or region (partial, case-insensitive)
- `status` (optional): Filter by accommodation status (`active` or `inactive`)
- `accommodationTypeId` (optional): Filter by a specific accommodation type id

### Distance-based Filtering
All three parameters must be provided together for distance filtering:
- `latitude` (optional): Latitude coordinate (-90 to 90)
- `longitude` (optional): Longitude coordinate (-180 to 180)  
- `radius` (optional): Search radius in kilometers (positive number)

### Pagination (Required)
- `pagination.page`: Page number (must be > 0)
- `pagination.pageSize`: Number of items per page (must be > 0)

### Sorting (Optional)
- `sorting.orderBy`: Field to sort by (`name`, `suburb`, `state`, `postcode`) - defaults to `name`
- `sorting.direction`: Sort direction (`asc` or `desc`) - defaults to `asc`

## Example Requests

### 1. Basic Search by Name
```
GET /api/accommodations/search?searchTerm=hotel&pagination.page=1&pagination.pageSize=10
```

### 2. Search with Location Filter
```
GET /api/accommodations/search?searchTerm=accommodation&location=melbourne&pagination.page=1&pagination.pageSize=10&sorting.orderBy=name&sorting.direction=asc
```

### 3. Distance-based Search
```
GET /api/accommodations/search?latitude=-37.8136&longitude=144.9631&radius=5&pagination.page=1&pagination.pageSize=10
```

### 4. Combined Search and Filters
```
GET /api/accommodations/search?searchTerm=hotel&location=victoria&status=active&accommodationTypeId=3&latitude=-37.8136&longitude=144.9631&radius=10&pagination.page=1&pagination.pageSize=20&sorting.orderBy=suburb&sorting.direction=desc
```

## Response Format

```json
{
  "data": [
    {
      "id": 1,
      "name": "Sample Hotel",
      "streetLine1": "123 Main St",
      "streetLine2": null,
      "suburb": "Melbourne",
      "state": "VIC",
      "postcode": "3000",
      "regionId": 1,
      "phone": "+61 3 1234 5678",
      "email": "<EMAIL>",
      "website": "https://samplehotel.com",
      "accommodationTypeId": 1,
      "densityId": 1,
      "durationIds": [1, 2],
      "inactive": false,
      "amenityIds": [1, 2, 3]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "totalRecords": 25,
    "totalPages": 3
  },
  "sort": {
    "orderBy": "name",
    "direction": "asc"
  }
}
```

## Search Logic

### Text Search (`searchTerm`)
Searches across the following fields (case-insensitive, partial match):
- Accommodation name
- Street address (line 1 and line 2)
- Suburb

### Location Filter (`location`)
Searches across the following fields (case-insensitive, partial match):
- Suburb
- State  
- Region name

### Status Filter (`status`)
- `active` returns accommodations with `inactive = false`
- `inactive` returns accommodations with `inactive = true`

### Accommodation Type Filter (`accommodationTypeId`)
- Filters accommodations that match the provided `accommodationTypeId`

### Distance Filter
- Uses Haversine formula for accurate distance calculation
- Filters accommodations within the specified radius from the given coordinates
- Only includes accommodations that have location data (latitude/longitude)
- Optimized with bounding box pre-filtering for better performance

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid query parameters. Please check pagination settings and distance filtering parameters (latitude, longitude, and radius must all be provided together)."
}
```

Common validation errors:
- Missing or invalid pagination parameters
- Partial distance filtering parameters (must provide all three: lat, lng, radius)
- Invalid coordinate ranges (latitude: -90 to 90, longitude: -180 to 180)
- Invalid radius (must be positive)

## Performance Notes

- Distance-based filtering uses bounding box optimization to reduce database load
- Precise distance calculation is performed in memory on the filtered result set
- For large datasets, consider adding database indexes on frequently searched fields
- Location data is optional - accommodations without coordinates are excluded from distance-based results

## Testing the Implementation

1. **Basic functionality**: Test with simple search terms and pagination
2. **Location filtering**: Test with various location terms (suburb, state, region)
3. **Distance filtering**: Test with different coordinates and radius values
4. **Combined filters**: Test with multiple filter types together
5. **Edge cases**: Test with invalid parameters, empty results, large page sizes
6. **Sorting**: Test different sort fields and directions
