using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;
    private readonly IMapper _mapper;
    private readonly IValidationService _validationService;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IAmenityOptionsRepository _amenityOptionsRepository;

    public AccommodationService(
        IAccommodationRepository accommodationRepository,
        IMapper mapper,
        IValidationService validationService,
        IRepository<Duration, int> durationRepository,
        IAmenityOptionsRepository amenityOptionsRepository)
    {
        _accommodationRepository = accommodationRepository;
        _mapper = mapper;
        _validationService = validationService;
        _durationRepository = durationRepository;
        _amenityOptionsRepository = amenityOptionsRepository;
    }

    public async Task<IEnumerable<AccommodationDto>> GetAll()
    {
        var accommodations = await _accommodationRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
    }

    public async Task<AccommodationDto?> GetById(long id)
    {
        var accommodation = await _accommodationRepository.GetByIdAsync(id);
        return accommodation != null ? _mapper.Map<AccommodationDto>(accommodation) : null;
    }

    public async Task<AccommodationDto> Create(AccommodationDto accommodationDto)
    {
        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core can populate the join table
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.Id))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();

        // Set timestamps for new entity using validation service
        // _validationService.SetTimestampsForCreate(accommodation);

        await _accommodationRepository.AddAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<AccommodationDto> Update(AccommodationDto accommodationDto)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodationDto.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodationDto.Id);
        }

        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core updates the join table correctly
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.Id))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // Set timestamps for updated entity using validation service
        // _validationService.SetTimestampsForUpdate(accommodation, existingAccommodation);

        await _accommodationRepository.UpdateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<bool> Delete(long id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        await _accommodationRepository.DeleteAsync(id);
        return true;
    }

    public async Task<(List<AccommodationDto> results, int totalCount)> SearchAccommodationsAsync(AccommodationSearchRequestDto request)
    {
        // Start with base predicate for soft delete filter
        Expression<Func<Accommodation, bool>> predicate = a => a.DiscardedAt == null;

        // Add search term filter if provided (search in name and address fields)
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            predicate = predicate.And(a =>
                a.Name.Contains(request.SearchTerm) ||
                a.StreetLine1.Contains(request.SearchTerm) ||
                (a.StreetLine2 != null && a.StreetLine2.Contains(request.SearchTerm)) ||
                a.Suburb.Contains(request.SearchTerm) || a.Postcode.Contains(request.SearchTerm));
        }

        // Add location filter if provided (search in suburb, state, and region)
        if (!string.IsNullOrWhiteSpace(request.Location))
        {
            predicate = predicate.And(a =>
                a.Suburb.Contains(request.Location) ||
                a.State.Contains(request.Location) ||
                a.Region.Name.Contains(request.Location));
        }

        if (!string.IsNullOrWhiteSpace(request.Status))
        {
            var matchInactive = request.Status.Equals("inactive", StringComparison.OrdinalIgnoreCase);
            predicate = predicate.And(a => a.Inactive == matchInactive);
        }

        if (request.AccommodationTypeId.HasValue)
        {
            predicate = predicate.And(a => a.AccommodationTypeId == request.AccommodationTypeId.Value);
        }

        // Fetch accommodations from repository with includes
        var accommodations = await _accommodationRepository.GetAllAsync(predicate, a => a.Region);
        var accommodationsList = accommodations.ToList();

        // Apply precise distance filtering if coordinates provided
        if (request.Latitude.HasValue && request.Longitude.HasValue && request.Radius.HasValue)
        {
            var bounds = CalculateBoundingBox(request.Latitude.Value, request.Longitude.Value, request.Radius.Value);

            accommodationsList = accommodationsList
                .Where(a => a.Location.HasValue &&
                            a.Location.Value.Latitude >= bounds.MinLat &&
                            a.Location.Value.Latitude <= bounds.MaxLat &&
                            a.Location.Value.Longitude >= bounds.MinLng &&
                            a.Location.Value.Longitude <= bounds.MaxLng)
                .Where(a =>
                    CalculateDistance(request.Latitude.Value, request.Longitude.Value,
                        a.Location!.Value.Latitude, a.Location.Value.Longitude) <= request.Radius.Value)
                .ToList();
        }

        // Get total count before pagination
        var totalCount = accommodationsList.Count;

        // Apply sorting
        var sortedAccommodations = ApplySorting(accommodationsList.AsQueryable(),
            request.Sorting?.OrderBy ?? "name",
            request.Sorting?.Direction ?? "asc");

        // Apply pagination
        var results = sortedAccommodations
            .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
            .Take(request.Pagination.PageSize)
            .ToList();

        return (_mapper.Map<List<AccommodationDto>>(results), totalCount);
    }

    private (double MinLat, double MaxLat, double MinLng, double MaxLng) CalculateBoundingBox(double centerLat, double centerLng, double radiusKm)
    {
        // Approximate degrees per kilometer
        const double latDegreesPerKm = 1.0 / 111.0;
        var lngDegreesPerKm = 1.0 / (111.0 * Math.Cos(centerLat * Math.PI / 180.0));

        var latOffset = radiusKm * latDegreesPerKm;
        var lngOffset = radiusKm * lngDegreesPerKm;

        return (
            MinLat: centerLat - latOffset,
            MaxLat: centerLat + latOffset,
            MinLng: centerLng - lngOffset,
            MaxLng: centerLng + lngOffset
        );
    }

    private double CalculateDistance(double lat1, double lng1, double lat2, double lng2)
    {
        // Haversine formula for calculating distance between two points on Earth
        const double earthRadiusKm = 6371.0;

        var dLat = (lat2 - lat1) * Math.PI / 180.0;
        var dLng = (lng2 - lng1) * Math.PI / 180.0;

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180.0) * Math.Cos(lat2 * Math.PI / 180.0) *
                Math.Sin(dLng / 2) * Math.Sin(dLng / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return earthRadiusKm * c;
    }

    private IQueryable<Accommodation> ApplySorting(IQueryable<Accommodation> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLower() switch
        {
            "name" => isDescending ? query.OrderByDescending(a => a.Name) : query.OrderBy(a => a.Name),
            "suburb" => isDescending ? query.OrderByDescending(a => a.Suburb) : query.OrderBy(a => a.Suburb),
            "state" => isDescending ? query.OrderByDescending(a => a.State) : query.OrderBy(a => a.State),
            "postcode" => isDescending ? query.OrderByDescending(a => a.Postcode) : query.OrderBy(a => a.Postcode),
            _ => query.OrderBy(a => a.Name) // Default sort by name
        };
    }
}
