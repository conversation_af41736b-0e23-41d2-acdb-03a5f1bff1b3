using EAMS.Domain.Aggregates;
using System.Linq.Expressions;

namespace EAMS.Domain.Repositories
{
    public interface IAccommodationRepository : IRepository<Accommodation, long>
    {
        /// <summary>
        /// Gets accommodations within a specified distance from a given point using database-level spatial queries
        /// </summary>
        /// <param name="basePredicate">Base predicate for filtering accommodations (e.g., soft delete, status filters)</param>
        /// <param name="latitude">Latitude of the search center point</param>
        /// <param name="longitude">Longitude of the search center point</param>
        /// <param name="radiusKm">Search radius in kilometers</param>
        /// <param name="includes">Navigation properties to include in the query</param>
        /// <returns>Accommodations within the specified distance</returns>
        Task<IEnumerable<Accommodation>> GetWithinDistanceAsync(
            Expression<Func<Accommodation, bool>> basePredicate,
            double latitude,
            double longitude,
            double radiusKm,
            params Expression<Func<Accommodation, object>>[] includes);
    }
}
