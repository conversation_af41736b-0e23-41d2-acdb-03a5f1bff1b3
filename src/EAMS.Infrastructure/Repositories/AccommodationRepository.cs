using EAMS.Domain.Aggregates;
using EAMS.Domain.Repositories;
using EAMS.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace EAMS.Infrastructure.Repositories
{
    public class AccommodationRepository : Repository<Accommodation, Int64>, IAccommodationRepository
    {
        private readonly EamsDbContext _context;

        public AccommodationRepository(EamsDbContext context) : base(context)
        {
            _context = context;
        }

        public override Task<IEnumerable<Accommodation>> GetAllAsync(
            Expression<Func<Accommodation, bool>>? where = null,
            params Expression<Func<Accommodation, object>>[] includes)
        {
            var defaultIncludes = new List<Expression<Func<Accommodation, object>>>
            {
                accommodation => accommodation.Duration,
                accommodation => accommodation.AmenityOptions
            };

            if (includes?.Length > 0)
            {
                defaultIncludes.AddRange(includes);
            }

            return base.GetAllAsync(where, defaultIncludes.ToArray());
        }

        public override Task<Accommodation?> GetByIdAsync(
            long id,
            params Expression<Func<Accommodation, object>>[] includes)
        {
            var defaultIncludes = new List<Expression<Func<Accommodation, object>>>
            {
                accommodation => accommodation.Duration,
                accommodation => accommodation.AmenityOptions
            };

            if (includes?.Length > 0)
            {
                defaultIncludes.AddRange(includes);
            }

            return base.GetByIdAsync(id, defaultIncludes.ToArray());
        }

        public async Task<IEnumerable<Accommodation>> GetWithinDistanceAsync(
            Expression<Func<Accommodation, bool>> basePredicate,
            double latitude,
            double longitude,
            double radiusKm,
            params Expression<Func<Accommodation, object>>[] includes)
        {
            // For now, let's implement this using a two-step approach:
            // 1. Get all accommodations matching the base predicate
            // 2. Filter by distance in memory (we'll optimize this later with proper spatial queries)

            // Start with the accommodations DbSet
            IQueryable<Accommodation> query = _context.Accommodations.AsNoTracking();

            // Apply default includes for accommodations
            var defaultIncludes = new List<Expression<Func<Accommodation, object>>>
            {
                accommodation => accommodation.Duration,
                accommodation => accommodation.AmenityOptions
            };

            // Add any additional includes provided
            if (includes?.Length > 0)
            {
                defaultIncludes.AddRange(includes);
            }

            // Apply all includes for eager loading of navigation properties
            query = defaultIncludes.Aggregate(query, (current, include) => current.Include(include));

            // Apply the base predicate (e.g., soft delete filter, status filters, etc.)
            query = query.Where(basePredicate);

            // Get all accommodations matching the base predicate
            var accommodations = await query.ToListAsync();

            // Filter by distance in memory using the Haversine formula
            // This is a temporary solution - in production, this should be done at the database level
            var filteredAccommodations = accommodations.Where(a =>
            {
                if (!a.Location.HasValue) return false;

                var distance = CalculateHaversineDistance(
                    latitude, longitude,
                    a.Location.Value.Latitude, a.Location.Value.Longitude);

                return distance <= radiusKm;
            });

            return filteredAccommodations;
        }

        private static double CalculateHaversineDistance(double lat1, double lng1, double lat2, double lng2)
        {
            // Haversine formula for calculating distance between two points on Earth
            const double earthRadiusKm = 6371.0;

            var dLat = (lat2 - lat1) * Math.PI / 180.0;
            var dLng = (lng2 - lng1) * Math.PI / 180.0;

            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(lat1 * Math.PI / 180.0) * Math.Cos(lat2 * Math.PI / 180.0) *
                    Math.Sin(dLng / 2) * Math.Sin(dLng / 2);

            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            return earthRadiusKm * c;
        }
    }
}
